const { groq } = require("@ai-sdk/groq");
const { google } = require("@ai-sdk/google");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const { NodeSDK } = require("@opentelemetry/sdk-node");
const { generateText: aiGenerateText } = require("ai");
const { LangfuseExporter } = require("langfuse-vercel");
const { configService } = require("./configService");

// Initialize SDK once at module load time
const sdk = new NodeSDK({
  traceExporter: new LangfuseExporter(),
  instrumentations: [getNodeAutoInstrumentations()],
});

// Start the SDK once
sdk.start();

// Handle graceful shutdown
process.on("SIGTERM", async () => {
  await sdk.shutdown();
});

process.on("SIGINT", async () => {
  await sdk.shutdown();
});

async function generateText(messages) {
  // Get current configuration
  const provider = configService.get("provider");
  const model = configService.get("model");
  const temperature = configService.get("temperature");

  // Build the config object
  const config = {
    model:
      provider === "groq"
        ? groq(model)
        : google(model, {
            useSearchGrounding: true,
            dynamicRetrievalConfig: {
              mode: "MODE_DYNAMIC",
              dynamicThreshold: 0.8,
            },
          }),
    messages: messages,
    temperature: temperature,
    // providerOptions: {
    //   google: { responseModalities: ["TEXT", "IMAGE"] },
    // },
    thinkingConfig: {
      thinkingBudget: 0,
    },
    experimental_telemetry: {
      isEnabled: true,
      functionId: "generateText",
    },
  };

  const result = await aiGenerateText(config);

  return result.text;
}

module.exports = {
  generateText,
};
